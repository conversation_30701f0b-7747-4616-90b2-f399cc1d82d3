import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Import types from our modular files
export type {
  AuthCallbackData,
  User,
  UserSession,
  AuthSettings,
  AuthStatus,
  AuthSuccessData,
  AuthErrorData,
  AuthStateChangeData
} from './auth';

export type {
  BackendStatusData
} from './backend';

// Expose protected methods that allow the renderer process to use IPC
contextBridge.exposeInMainWorld('electron', {
  // Authentication flow methods
  openExternalUrl: (url: string) => ipcRenderer.invoke('open-external-url', url),
  getAuthUrls: () => ipcRenderer.invoke('get-auth-urls'),

  // === AUTHENTICATION IPC METHODS ===

  // Get current authentication status
  getAuthStatus: () => ipcRenderer.invoke('auth:getStatus'),

  // Get current user's access token
  getAccessToken: () => ipcRenderer.invoke('auth:getAccessToken'),

  // Logout current user
  logout: () => ipcRenderer.invoke('auth:logout'),

  // Update user activity (extend session)
  updateActivity: () => ipcRenderer.invoke('auth:updateActivity'),

  // Get current user
  getCurrentUser: () => ipcRenderer.invoke('auth:getCurrentUser'),

  // Get user settings
  getUserSettings: (userId?: string) => ipcRenderer.invoke('auth:getUserSettings', userId),

  // Process authentication callback (for manual triggering)
  processAuthCallback: (callbackData: any) => ipcRenderer.send('auth:processCallback', callbackData),

  // === AUTHENTICATION EVENT LISTENERS ===

  // Authentication callback listener (OAuth callback URL processed)
  onAuthCallback: (callback: (data: any) => void) => {
    const subscription = (_event: any, data: any) => callback(data);
    ipcRenderer.on('auth-callback', subscription);

    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('auth-callback', subscription);
    };
  },

  // Authentication success listener
  onAuthSuccess: (callback: (data: any) => void) => {
    const subscription = (_event: any, data: any) => callback(data);
    ipcRenderer.on('auth-success', subscription);

    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('auth-success', subscription);
    };
  },

  // Authentication error listener
  onAuthError: (callback: (data: any) => void) => {
    const subscription = (_event: any, data: any) => callback(data);
    ipcRenderer.on('auth-error', subscription);

    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('auth-error', subscription);
    };
  },

  // Authentication state change listener
  onAuthStateChanged: (callback: (data: any) => void) => {
    const subscription = (_event: any, data: any) => callback(data);
    ipcRenderer.on('auth-state-changed', subscription);

    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('auth-state-changed', subscription);
    };
  },

  // === DATABASE IPC (for settings, etc.) ===

  // Settings database methods
  getSettings: () => ipcRenderer.invoke('settings:get'),
  updateSettings: (updates: any) => ipcRenderer.invoke('settings:update', updates),

  // === BACKEND COMMUNICATION ===

  // Backend communication
  sendCommand: (type: string, args?: any) => ipcRenderer.invoke('send-command', type, args),
  getBackendStatus: () => ipcRenderer.invoke('python-get-status'),
  reconnectBackend: () => ipcRenderer.invoke('python-reconnect'),

  // Backend WebSocket message listener
  onBackendMessage: (callback: (message: any) => void) => {
    const subscription = (_event: any, message: any) => callback(message);
    ipcRenderer.on('python-ws-message', subscription);

    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('python-ws-message', subscription);
    };
  },

  // Backend connection status listeners
  onBackendConnected: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('python-ws-connected', subscription);

    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('python-ws-connected', subscription);
    };
  },

  onBackendConnectionFailed: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('python-ws-connection-failed', subscription);

    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('python-ws-connection-failed', subscription);
    };
  },

  // Backend server welcome listener
  onBackendServerWelcome: (callback: (data: any) => void) => {
    const subscription = (_event: any, data: any) => callback(data);
    ipcRenderer.on('python-server-welcome', subscription);

    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('python-server-welcome', subscription);
    };
  }
});
